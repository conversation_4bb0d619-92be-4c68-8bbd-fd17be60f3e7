import { serve } from '@hono/node-server';
import { app } from './routes.js';

const port = parseInt(process.env.PORT || '8080');

const server = serve({ fetch: app.fetch, port });
console.log(`Server is running on port ${port}`);

process.on('SIGINT', () => {
	console.log('Received SIGINT, shutting down gracefully...');
	server.close();
	process.exit(0);
});

process.on('SIGTERM', () => {
	console.log('Received SIGTERM, shutting down gracefully...');
	server.close((err) => {
		if (err) {
			console.error('Error during shutdown:', err);
			process.exit(1);
		}
		process.exit(0);
	});
});
